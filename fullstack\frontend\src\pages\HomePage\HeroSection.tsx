import React from "react";
import { HiTrash, HiPlus } from "react-icons/hi2";
import { IoShirtOutline } from "react-icons/io5";
import { getConfidenceColor } from "../../utils/colorUtils";

interface Pick {
  id: string;
  playerName: string;
  playerNumber: string;
  betType: string;
  gameInfo: string;
  confidence?: number;
  handicapperName?: string;
}

interface HeroSectionProps {
  totalPicksCount: number;
  userPicks: Pick[];
  isOptimizing: boolean;
  optimizerError: string;
  onAddPicksClick: () => void;
  onOptimizeParlays: () => void;
  onRemovePick: (id: string) => void;
}

function HeroSection({
  totalPicksCount,
  userPicks,
  isOptimizing,
  optimizerError,
  onAddPicksClick,
  onOptimizeParlays,
  onRemovePick,
}: HeroSectionProps) {
  return (
    <div className="h-screen bg-[#061844] text-[#58C612] flex flex-col items-center justify-center p-4 select-none overflow-visible">
      {/* Logo Section */}
      <div className="w-full h-[100px] flex justify-center mb-auto mt-10">
        <header className="flex items-center justify-center">
          <img
            src="/project_parlay_logo.png"
            alt="Project Parlay Logo"
            className="w-auto h-32 sm:h-40 select-none transition-all duration-300 ease-in-out"
          />
        </header>
      </div>

      {totalPicksCount === 0 ? (
        // Empty state
        <div className="text-center mx-auto mb-auto mt-[-25%] sm:mt-[-10%]">
          <h2 className="text-white text-[400%] font-bold text-center">
            Time for a fresh start
          </h2>

          <p className="text-white text-[125%] sm:text-[200%] mb-8">
            You don't have any picks in your list
          </p>

          <button
            onClick={onAddPicksClick}
            className="px-8 py-4 bg-[#233e6c] hover:bg-[#232d6c] text-white w-[60%] font-bold rounded-lg text-[150%] sm:text-[200%] transition-all ease-linear duration-300 shadow-lg hover:shadow-xl hover:cursor-pointer"
          >
            Add Picks
          </button>
        </div>
      ) : (
        // My Picks display
        <div className="w-full max-w-7xl mx-auto px-4 mb-auto mt-4" style={{ overflow: 'visible', position: 'relative' }}>
          <div className="mb-6">
            <h2 className="text-white text-3xl sm:text-4xl font-bold">
              My Picks ({totalPicksCount})
            </h2>
          </div>

          {/* 2-column grid of picks */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {userPicks.map((pick) => (
              <div
                key={pick.id}
                className="bg-[#233e6c] rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 ease-linear hover:cursor-pointer hover:scale-[101.5%]"
              >
                <div className="flex flex-row gap-6 my-auto justify-center items-center">
                  {/* Player info */}
                  <div className="flex flex-col items-center md:flex-1 min-w-0">
                    <div
                      className="w-32 h-32 rounded-full mb-2 flex items-center justify-center bg-gradient-to-b from-gray-800 to-gray-900 relative overflow-hidden"
                      style={{
                        border: `4px solid ${getConfidenceColor(pick.confidence || 75)}`,
                      }}
                    >
                      <IoShirtOutline
                        className="w-20 h-20 absolute"
                        style={{ color: getConfidenceColor(pick.confidence || 75) }}
                      />
                      <div className="text-white font-bold text-lg sm:text-xl z-10 relative">
                        {pick.playerNumber}
                      </div>
                    </div>
                    <h3 className="font-bold text-lg text-center">
                      {pick.playerName}
                    </h3>
                    <p className="text-sm text-center text-white">{pick.betType}</p>
                    <p className="text-xs text-gray-400 text-center">
                      {pick.gameInfo}
                    </p>
                    {pick.handicapperName && (
                      <p className="text-blue-400 text-xs mt-1 text-center">
                        Recommended by {pick.handicapperName}
                      </p>
                    )}
                  </div>

                  {/* Confidence and remove button */}
                  <div className="flex flex-col items-center gap-2 md:flex-1 min-w-0 m-auto h-full">
                    <div className="text-center flex flex-col items-center justify-start h-full">
                      <div
                        className="text-[75px] md:text-[100px] font-bold mt-[-24px]"
                        style={{ color: getConfidenceColor(pick.confidence || 75) }}
                      >
                        {pick.confidence || 75}
                      </div>
                      <div className="text-[24px] md:text-[24px] font-bold text-white mt-[-24px]">
                        Confidence
                        <br />
                        Rating
                      </div>
                    </div>
                    <button
                      onClick={() => onRemovePick(pick.id)}
                      className="p-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-all duration-300 hover:scale-105 shadow-md hover:shadow-lg hover:cursor-pointer"
                      title="Remove pick"
                    >
                      <HiTrash className="w-8 h-8 hover:cursor-pointer" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Action Buttons Container */}
          <div className="mt-8 relative">
            {/* Centered Optimize Parlays button */}
            <div className="flex justify-center">
              <button
                onClick={onOptimizeParlays}
                disabled={isOptimizing || totalPicksCount < 2}
                className={`px-12 py-6 font-bold rounded-lg text-2xl transition-all ease-linear duration-300 shadow-lg hover:shadow-xl ${
                  isOptimizing || totalPicksCount < 2
                    ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                    : 'bg-[#233e6c] hover:bg-[#1a2d54] text-white hover:cursor-pointer'
                }`}
              >
                {isOptimizing ? 'Optimizing...' : 'Optimize Parlays'}
              </button>
            </div>

            {/* Right-aligned Add More Picks button */}
            <button
              onClick={onAddPicksClick}
              className="absolute top-0 right-0 px-6 py-3 bg-[#233e6c] hover:bg-[#1a2d54] text-white font-bold rounded-lg text-lg transition-all ease-linear duration-300 shadow-lg hover:shadow-xl hover:cursor-pointer flex items-center gap-2"
            >
              <HiPlus className="w-5 h-5" />
              Add More Picks
            </button>
          </div>

          {/* Error Display */}
          {optimizerError && (
            <div className="mt-8 p-4 bg-red-900 bg-opacity-50 border border-red-500 rounded-xl">
              <p className="text-red-400 text-center">
                <strong>Error:</strong> {optimizerError}
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

export default HeroSection;
