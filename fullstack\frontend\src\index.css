@import 'tailwindcss';

/* Custom scrollbar styles for handicapper grids */
.handicapper-scroll {
  scrollbar-width: thin;
  scrollbar-color: #3b82f6 #1f2937;
  padding-right: 8px; /* Add padding to move content away from scrollbar */
}

.handicapper-scroll::-webkit-scrollbar {
  width: 6px;
}

.handicapper-scroll::-webkit-scrollbar-track {
  background: #1f2937;
  border-radius: 3px;
  margin-right: 2px; /* Add margin to move scrollbar further right */
}

.handicapper-scroll::-webkit-scrollbar-thumb {
  background: #3b82f6;
  border-radius: 3px;
}

.handicapper-scroll::-webkit-scrollbar-thumb:hover {
  background: #60a5fa;
}

/* Custom scrollbar styles for parlay containers */
.parlay-scroll {
  scrollbar-width: thin;
  scrollbar-color: #58C612 #233e6c;
}

.parlay-scroll::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.parlay-scroll::-webkit-scrollbar-track {
  background: #233e6c;
  border-radius: 3px;
}

.parlay-scroll::-webkit-scrollbar-thumb {
  background: #58C612;
  border-radius: 3px;
}

.parlay-scroll::-webkit-scrollbar-thumb:hover {
  background: #6fd015;
}

.parlay-scroll::-webkit-scrollbar-corner {
  background: #233e6c;
}